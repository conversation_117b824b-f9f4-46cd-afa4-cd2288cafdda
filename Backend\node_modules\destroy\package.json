{"name": "destroy", "description": "destroy a stream if possible", "version": "1.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "stream-utils/destroy", "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.2", "nyc": "15.1.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "files": ["index.js", "LICENSE"], "keywords": ["stream", "streams", "destroy", "cleanup", "leak", "fd"]}